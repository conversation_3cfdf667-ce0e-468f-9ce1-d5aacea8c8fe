package com.alipay.codegencore.service.codegpt;

import cn.hutool.core.lang.Pair;
import com.alibaba.fastjson.JSONArray;
import com.alibaba.fastjson.JSONObject;
import com.alipay.codegencore.model.domain.ChatMessageDO;
import com.alipay.codegencore.model.domain.ChatSessionDO;
import com.alipay.codegencore.model.domain.SceneDO;
import com.alipay.codegencore.model.enums.ConversationTypeEnum;
import com.alipay.codegencore.model.enums.FileAnnotationTypeEnum;
import com.alipay.codegencore.model.enums.ReviewPlatformEnum;
import com.alipay.codegencore.model.model.*;
import com.alipay.codegencore.model.request.CodeGPTVoteRequestBean;
import com.alipay.codegencore.model.response.NewPluginStreamPartResponse;

import javax.servlet.http.HttpServletResponse;
import java.util.Date;
import java.util.List;

/**
 * 对话服务
 * <AUTHOR>
 */
public interface ChatMessageService {

    /**
     * 重新生成回答
     * @param sessionId 会话id
     * @return 回复内容
     */
    void regenerationAnswer(HttpServletResponse httpServletResponse, String sessionId,String uid, Boolean remoteAgent,String answerUid);

    /**
     * 提问并获取流式输出
     * @param sessionUid
     * @param content
     * @return
     */
    void conversation(HttpServletResponse httpServletResponse, String sessionUid, String content,Boolean isModified, Boolean remoteAgent, String answerUid, Boolean tryRepoSearch, JSONObject extraInfo);

    /**
     * 提交表单，继续对话
     * @param httpServletResponse
     * @param uid
     * @param data
     */
    void continueConversation(HttpServletResponse httpServletResponse, String uid, JSONObject data, Boolean remoteAgent);

    /**
     * 根据用户提问从session的文档库中搜索对应的片段
     * session的文档库包括agent文档库和用户在会话期间上传的文档列表
     * @param query 用户提问
     * @param chatSessionDO 会话信息
     * @return 搜索结果，包括文档id和搜索到的片段内容
     */
    List<SegmentInfo> searchDoc(String query, ChatSessionDO chatSessionDO);

    /**
     * 根据用户提问从session的文档库中搜索对应的片段
     * session的文档库包括agent文档库和用户在会话期间上传的文档列表
     * @param query 用户提问
     * @param chatSessionDO 会话信息
     * @param minSimilarity 最小相似度阈值，如果为null则使用配置中的默认值
     * @return 搜索结果，包括文档id和搜索到的片段内容
     */
    List<SegmentInfo> searchDoc(String query, ChatSessionDO chatSessionDO, Double minSimilarity);

    /**
     * 获取对话类型
     * @param scene
     * @return
     */
    ConversationTypeEnum getConversationType(SceneDO scene);

    /**
     * 获取最新生成的一条消息的uid
     * @param sessionUid 会话id
     * @return
     */
    List<ChatMessageDO> getLatestAssistantMessage(String sessionUid);

    /**
     * 获取会话内容全部对话内容
     * @param sessionUid 会话uid
     * @param rewriteFileAnnotation 是否重写文件注释
     * @param showMultiGeneratedAnswer 是否展示生成多次的答案
     * @return
     */
    List<ChatMessageDO> listChatMessage(String sessionUid, boolean showMultiGeneratedAnswer, boolean rewriteFileAnnotation);

    /**
     * 根据msg的uid获取一条msg
     * @param uid 参数
     * @return msg
     */
    ChatMessageDO getChatMessageByUid(String uid);

    /**
     * 根据一条消息的uid,获取这轮消息的问答内容
     * @param uid
     * @return 一问N答
     */
    List<ChatMessageDO> getChatMessagePairByUid(String uid);

    /**
     * 更新 codeGPTVoteRequestBean 通过uid
     * @param codeGPTVoteRequestBean uid必须要有
     * @return 更新条数
     */
    void updateChatMessage(CodeGPTVoteRequestBean codeGPTVoteRequestBean);
    /**
     * 更新模型回复的代码所属语言
     * @param uid 消息id
     * @param languages 要修改的语言
     * @return 更新条数
     */
    int putLanguages(String uid, String languages);

    /**
     * 获取消息的参考链接
      * @param uid msg的uid
     * @return JSONArray
     */
    JSONArray getMessageReference(String uid);

    /**
     * 删除一个会话中的全部消息
     * @param sessionUid 会话id
     */
    void deleteAllMessage(String sessionUid);

    /**
     * 更新审核结果
     *
     * @param messageUid
     * @param reviewResultModelPair
     */
    int updateContentReviewResult(String messageUid, Pair<ReviewPlatformEnum, ReviewResultModel> reviewResultModelPair);

    /**
     * 更新list中AI回复的对象的content，把审核不通过的content替换掉
     * @param chatMessageDOList list必须是按照1问1-N答的顺序来传参
     */
    void updateCheckFailedContent(List<ChatMessageDO> chatMessageDOList);

    /**
     * 获取蚂蚁+的众筹接口所需的汇总数据
     * @param gmtCreateBegin 开始时间
     * @param gmtCreateEnd 结束时间
     * @param empId 工号 可以不传
     * @param needEmpList 是否需要按照工号分组返回
     * @return
     */
    MaYiDevMessageCountModel getMaYiDeveloperCount(Date gmtCreateBegin, Date gmtCreateEnd, String empId, boolean needEmpList);

    /**
     * 获取会话文件摘要信息
     *
     * @param sessionFileSummaryRequest 会话文件摘要请求对象，包含sessionId和userId等必要参数
     * @return 返回会话文件摘要列表
     */
    String bindSessionFileAndGetSummary(SessionFileSummaryRequest sessionFileSummaryRequest);

    /**
     * 根据sessionUid获取对应的会话文件响应结果列表
     *
     * @param sessionUid 会话UID
     * @return 返回会话文件响应结果列表
     */
    List<SessionFileResponse> getSessionFileResponse(String sessionUid);

    /**
     * 在ExtInfo添加配置信息
     * @param sessionUid 会话UID
     * @param sessionExtInfo 需要添加的配置信息
     * @return
     */
    Boolean addSessionExtInfo(String sessionUid, JSONObject sessionExtInfo);

    /**
     * 获取ExtInfo
     * @param sessionUid 会话UID
     * @return
     */
    JSONObject getSessionExtInfo(String sessionUid);

    /**
     *
     * @param sessionUid
     * @param keys
     * @return
     */
    Boolean deleteSessionExtInfo(String sessionUid, List<String> keys);

    /**
     * 更新消息信息
     * @param chatMessageDO
     * @return
     */
    Boolean updateMessage(ChatMessageDO chatMessageDO);

    /**
     * 根据uid查询当前轮会话有多少ai回复
     *
     * @param sessionUid
     * @return
     */
    Long selectLastMessageCount(String sessionUid, Long queryIndex);

    /**
     * 重写文件注解
     * @param newPluginStreamPartResponse
     * @param docsInfo
     * @return
     */
    void processStreamResponse(NewPluginStreamPartResponse newPluginStreamPartResponse,
                               JSONObject docsInfo,
                               HttpServletResponse httpServletResponse);

    /**
     * 重写文件注解
     * @param newPluginStreamPartResponse
     * @param docsInfo
     * @return
     */
    void processStreamResponse(NewPluginStreamPartResponse newPluginStreamPartResponse,
                               JSONObject docsInfo, Boolean remoteAgent, ChatSessionDO chatSessionDO,
                               HttpServletResponse httpServletResponse);

    /**
     * 重写文件注解
     *
     * @param originStr
     * @param docsInfo
     * @return
     */
    String rewriteFileAnnotation(String originStr, JSONObject docsInfo, FileAnnotationTypeEnum fileAnnotationType);
}
